# AI 英语学习助手 - 前端应用

## 应用简介

这是一个基于 Vue 3 开发的 AI 英语学习助手前端应用，旨在为英语学习者提供智能化的学习指导和支持。

## 主要功能

### 1. 双模式学习系统

#### 普通聊天模式 💬
- 与 AI 英语导师进行自由对话
- 适合日常英语交流练习
- 支持实时语法纠错和建议
- 个性化学习指导

#### 知识库问答模式 🧠
- 基于专业英语学习知识库
- 涵盖语法、口语、听力、阅读、写作等各个方面
- 提供权威学习资料来源引用
- 系统化的学习内容推荐

### 2. 英语学习主题分类

#### 📚 语法学习
- 英语时态详解
- 定语从句用法
- 虚拟语气解析
- 被动语态构成

#### 🗣️ 口语练习
- 发音技巧训练
- 流利度提升方法
- 日常对话场景
- 紧张情绪克服

#### 🎧 听力训练
- 听力技巧培养
- 不同口音识别
- 新闻播客理解
- 考试应试策略

#### 📖 阅读理解
- 阅读速度提升
- 长难句解析
- 词汇量扩展
- 理解技巧掌握

#### ✍️ 写作技巧
- 作文结构设计
- 议论文写作
- 商务邮件格式
- 常见错误避免

#### 📝 词汇扩展
- 单词记忆方法
- 词根词缀学习
- 同义词辨析
- 专业词汇积累

#### 🎯 考试备考
- 托福雅思策略
- 四六级技巧
- GRE/GMAT准备
- 商务英语考试

#### 💼 商务英语
- 职场沟通技能
- 会议演讲技巧
- 商务谈判英语
- 国际商务礼仪

## 技术特性

### 前端技术栈
- **Vue 3**: 现代化的前端框架
- **Vite**: 快速的构建工具
- **Axios**: HTTP 请求库
- **Marked**: Markdown 渲染
- **Server-Sent Events**: 流式对话体验

### 用户体验
- 📱 响应式设计，支持移动端
- 🔄 实时流式对话
- 🎨 直观的界面设计
- 📊 学习进度跟踪
- 🔍 智能搜索建议

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 项目结构

```
src/
├── api/                    # API 接口
│   ├── chatApi.js         # 普通聊天 API
│   └── ragApi.js          # RAG 知识库 API
├── components/            # Vue 组件
│   ├── ChatInput.vue      # 聊天输入框
│   ├── ChatMessage.vue    # 普通消息组件
│   ├── ChatModeToggle.vue # 模式切换组件
│   ├── LoadingDots.vue    # 加载动画
│   └── RagMessage.vue     # RAG 消息组件
├── config/                # 配置文件
│   └── englishTopics.js   # 英语学习主题配置
├── utils/                 # 工具函数
│   └── index.js          # 通用工具
├── App.vue               # 主应用组件
└── main.js              # 应用入口
```

## API 接口

### 普通聊天接口
- `GET /api/ai/chat` - SSE 流式聊天

### RAG 知识库接口
- `GET /api/ai/rag/chat-stream` - RAG 流式问答
- `POST /api/ai/rag/search` - 知识库搜索
- `GET /api/ai/rag/stats` - 知识库统计

## 学习建议

### 初学者
1. 从语法学习开始，建立基础
2. 配合词汇扩展，增加表达能力
3. 通过听力训练提高理解能力

### 中级学习者
1. 重点练习口语表达
2. 加强阅读理解训练
3. 开始尝试写作练习

### 高级学习者
1. 专注商务英语应用
2. 准备各类英语考试
3. 深入文化背景学习

## 个性化学习路径

应用会根据用户的学习水平和目标，推荐个性化的学习内容和练习方法，帮助用户更高效地提升英语水平。

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎通过以下方式联系我们：

- 创建 Issue
- 发送邮件反馈
- 参与社区讨论

让我们一起打造更好的英语学习体验！🚀