# AI 编程小助手 - RAG 前端功能指南

## 🎯 功能概述

前端项目已成功集成 RAG（Retrieval Augmented Generation）功能，现在支持两种聊天模式：

### 💬 普通聊天模式
- 与AI助手进行通用对话
- 适合开放性问题和创意讨论
- 支持各种编程相关的咨询

### 🧠 知识问答模式
- 基于专业知识库的智能问答
- 显示知识来源和相似度
- 专注于编程学习、面试准备、求职指导

## 🔧 新增组件

### 1. RagMessage.vue
专门用于显示RAG回复的消息组件，特色功能：
- 显示知识来源信息
- 支持展开/收起源文档
- 特殊的RAG样式和图标
- 相似度分数显示

### 2. ChatModeToggle.vue
模式切换组件，提供：
- 普通聊天/知识问答模式切换
- 实时显示知识库统计信息
- 主题标签快速开始功能
- 模式说明和使用提示

### 3. ragApi.js
RAG相关的API接口：
- `ragChat()` - 同步RAG问答
- `ragChatStream()` - 流式RAG问答
- `searchKnowledge()` - 知识库搜索
- `getKnowledgeStats()` - 获取知识库统计
- `batchSearchKnowledge()` - 批量搜索

## 🎨 界面特色

### 视觉区别
- **普通模式**: 灰色头像 (AI)，传统聊天样式
- **RAG模式**: 渐变紫色头像 (🧠)，特殊气泡样式

### 交互功能
- 模式切换后保持各自的聊天历史
- RAG模式显示搜索建议浮窗
- 主题标签一键生成相关问题
- 知识源可展开查看详细内容

### 响应式设计
- 移动端适配优化
- 搜索建议在小屏幕上全宽显示
- 自适应布局和字体大小

## 🚀 使用方法

### 启动应用

1. **安装依赖**：
```bash
cd /Users/<USER>/Desktop/RAG/ai-code-helper-master/ai-code-helper-frontend
npm install
```

2. **启动开发服务器**：
```bash
npm run dev
```

3. **访问应用**：
打开浏览器访问 `http://localhost:5173`

### 功能演示

#### 1. 模式切换
- 点击顶部的"普通聊天"或"知识问答"标签
- 观察界面样式和提示信息的变化
- 每种模式独立维护聊天历史

#### 2. RAG功能体验
- 切换到"知识问答"模式
- 点击主题标签（如"Java学习路线"）
- 或直接输入专业问题
- 观察AI回复中的知识来源显示

#### 3. 知识源查看
- 在RAG回复中点击"展开/收起"查看知识来源
- 点击具体的源文档查看详细内容
- 观察相似度分数和文件名信息

## 📱 界面截图

### 模式切换界面
```
┌─────────────────────────────────┐
│  💬 普通聊天  │  🧠 知识问答     │
├─────────────────────────────────┤
│ 与AI助手进行通用对话            │
│ 适合开放性问题和创意讨论        │
├─────────────────────────────────┤
│ 📚 知识库信息                   │
│ 文档数量: 4                     │
│ 存储类型: 内存向量数据库        │
│ 嵌入模型: text-embedding-004    │
├─────────────────────────────────┤
│ 可咨询主题:                     │
│ [Java学习路线] [Spring Boot开发] │
│ [MySQL数据库] [Redis缓存]       │
└─────────────────────────────────┘
```

### RAG回复示例
```
┌─────────────────────────────────┐
│ 🧠  Java学习需要系统化的规划... │
│     ├─ 第一阶段：基础语法      │
│     ├─ 第二阶段：面向对象      │
│     └─ 第三阶段：框架学习      │
│                                 │
│     📚 知识来源 (2) [展开]      │
│     ├─ 📄 Java 编程学习路线.md │
│     └─ 📄 鱼皮的项目学习建议.md │
└─────────────────────────────────┘
```

## 🔧 技术实现

### 状态管理
- 使用独立的消息数组管理不同模式的聊天历史
- 实时切换模式而不丢失对话内容
- 独立的内存ID管理各模式的会话状态

### API集成
- 普通模式调用 `/ai/chat` 接口
- RAG模式调用 `/ai/rag/chat-stream` 接口
- 统一的错误处理和重连机制
- 支持流式和同步两种响应方式

### 组件通信
- 使用事件总线进行组件间通信
- 模式切换事件和主题选择事件
- 统一的消息格式和状态管理

## 🐛 故障排除

### 常见问题

1. **RAG模式无响应**
   - 检查后端RAG服务是否启动
   - 确认 `http://localhost:8081/api/ai/rag/stats` 可访问
   - 查看浏览器控制台错误信息

2. **知识库统计信息显示异常**
   - 确认后端RAG配置正确
   - 检查API密钥配置
   - 重启后端服务重新初始化

3. **模式切换后消息丢失**
   - 这是正常行为，不同模式维护独立的聊天历史
   - 切换回原模式可看到之前的消息

### 调试方法

1. **开启调试日志**：
在浏览器控制台中可以看到详细的API调用和状态变化日志

2. **检查网络请求**：
打开浏览器开发者工具的网络面板，观察API调用状态

3. **后端日志**：
参考后端的向量化日志来诊断RAG相关问题

## 🎯 下一步计划

### 功能扩展
- [ ] 支持知识库文档的在线管理
- [ ] 添加搜索历史和收藏功能
- [ ] 实现多轮对话的上下文保持
- [ ] 支持文件上传和解析

### 性能优化
- [ ] 消息列表虚拟滚动
- [ ] API响应缓存机制
- [ ] 组件懒加载优化
- [ ] 移动端性能提升

### 用户体验
- [ ] 暗色主题支持
- [ ] 快捷键操作
- [ ] 语音输入功能
- [ ] 导出对话记录

## 📞 技术支持

如果遇到问题，可以：
1. 查看浏览器控制台的错误信息
2. 检查后端服务状态和日志
3. 参考RAG API指南文档
4. 确认前后端版本兼容性

这个RAG增强的前端为AI编程助手提供了更专业、更准确的问答体验！ 