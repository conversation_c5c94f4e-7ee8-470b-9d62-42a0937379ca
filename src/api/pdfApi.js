import axios from 'axios'

// 配置axios基础URL
const API_BASE_URL = 'http://localhost:8081/api'

/**
 * 导出AI回复内容为PDF
 * @param {string} content AI回复的内容
 * @param {string} title PDF文档标题
 * @returns {Promise<Blob>} 返回PDF文件的Blob对象
 */
export async function exportToPdf(content, title = 'AI回复内容') {
    try {
        const response = await axios.post(`${API_BASE_URL}/ai/export/pdf`, {
            content: content,
            title: title
        }, {
            responseType: 'blob', // 重要：告诉axios这是二进制数据
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 30000 // 30秒超时，PDF生成可能需要较长时间
        })
        
        return response.data
    } catch (error) {
        console.error('PDF导出失败:', error)
        throw error
    }
}

/**
 * 下载文件
 * @param {Blob} blob 文件数据
 * @param {string} filename 文件名
 */
export function downloadFile(blob, filename) {
    try {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        
        // 触发下载
        document.body.appendChild(link)
        link.click()
        
        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        console.log('文件下载成功:', filename)
    } catch (error) {
        console.error('文件下载失败:', error)
        throw error
    }
}

/**
 * 生成PDF文件名
 * @param {string} title 标题
 * @returns {string} 文件名
 */
export function generatePdfFileName(title) {
    // 获取当前时间
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_')
    
    // 清理标题，移除特殊字符
    const cleanTitle = title ? title.replace(/[^\w\u4e00-\u9fa5]/g, '_') : 'AI回复'
    
    return `${cleanTitle}_${timestamp}.pdf`
}

/**
 * 完整的PDF导出和下载流程
 * @param {string} content AI回复内容
 * @param {string} title 文档标题
 * @returns {Promise<void>}
 */
export async function exportAndDownloadPdf(content, title) {
    try {
        // 验证输入
        if (!content || content.trim() === '') {
            throw new Error('内容不能为空')
        }
        
        // 导出PDF
        const pdfBlob = await exportToPdf(content, title)
        
        // 生成文件名
        const filename = generatePdfFileName(title)
        
        // 下载文件
        downloadFile(pdfBlob, filename)
        
        return { success: true, filename }
    } catch (error) {
        console.error('PDF导出下载失败:', error)
        throw error
    }
}