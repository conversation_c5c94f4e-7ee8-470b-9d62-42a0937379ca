import axios from 'axios';

// 基础API配置
const API_BASE_URL = 'http://localhost:8081/api';

/**
 * 语音合成API模块
 */
export const speechApi = {
  /**
   * 基础语音合成 - 将文本转换为语音文件
   * @param {string} text - 要转换的文本内容
   * @param {string} title - 文章标题（可选）
   * @returns {Promise} 返回语音合成结果
   */
  async synthesizeText(text, title = '') {
    try {
      const response = await axios.post(`${API_BASE_URL}/ai/speech/synthesize`, {
        text: text,
        title: title
      });
      return response.data;
    } catch (error) {
      console.error('语音合成失败:', error);
      throw error;
    }
  },

  /**
   * 自定义语音合成 - 使用指定的语音进行合成
   * @param {string} text - 要转换的文本内容
   * @param {string} voiceName - 语音名称
   * @param {string} title - 文章标题（可选）
   * @returns {Promise} 返回语音合成结果
   */
  async synthesizeWithVoice(text, voiceName, title = '') {
    try {
      const response = await axios.post(`${API_BASE_URL}/ai/speech/synthesize-with-voice`, {
        text: text,
        title: title,
        voiceName: voiceName
      });
      return response.data;
    } catch (error) {
      console.error('自定义语音合成失败:', error);
      throw error;
    }
  },

  /**
   * 获取支持的语音列表
   * @returns {Promise} 返回支持的语音列表
   */
  async getSupportedVoices() {
    try {
      const response = await axios.get(`${API_BASE_URL}/ai/speech/voices`);
      return response.data;
    } catch (error) {
      console.error('获取语音列表失败:', error);
      throw error;
    }
  },

  /**
   * 获取语音文件列表
   * @returns {Promise} 返回语音文件列表
   */
  async getSpeechFiles() {
    try {
      const response = await axios.get(`${API_BASE_URL}/ai/speech/files`);
      return response.data;
    } catch (error) {
      console.error('获取语音文件列表失败:', error);
      throw error;
    }
  },

  /**
   * 下载语音文件
   * @param {string} fileName - 文件名
   * @returns {Promise} 返回文件下载响应
   */
  async downloadSpeechFile(fileName) {
    try {
      const response = await axios.get(`${API_BASE_URL}/ai/speech/download`, {
        params: { fileName },
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('下载语音文件失败:', error);
      throw error;
    }
  },

  /**
   * 获取语音文件的播放URL
   * @param {string} fileName - 文件名
   * @returns {string} 返回语音文件的URL
   */
  getSpeechFileUrl(fileName) {
    return `${API_BASE_URL}/ai/speech/download?fileName=${encodeURIComponent(fileName)}`;
  },

  /**
   * 创建音频播放器并播放语音
   * @param {string} fileName - 文件名
   * @returns {Promise<HTMLAudioElement>} 返回音频元素
   */
  async playSpeechFile(fileName) {
    return new Promise((resolve, reject) => {
      const audio = new Audio(this.getSpeechFileUrl(fileName));
      
      audio.addEventListener('loadeddata', () => {
        resolve(audio);
      });
      
      audio.addEventListener('error', (error) => {
        reject(error);
      });
      
      audio.load();
    });
  },

  /**
   * 将文本合成为语音并直接播放
   * @param {string} text - 要转换的文本
   * @param {string} voiceName - 语音名称（可选）
   * @param {string} title - 标题（可选）
   * @returns {Promise<HTMLAudioElement>} 返回音频元素
   */
  async synthesizeAndPlay(text, voiceName = null, title = '') {
    try {
      // 步骤1: 合成语音
      const synthesisResult = voiceName 
        ? await this.synthesizeWithVoice(text, voiceName, title)
        : await this.synthesizeText(text, title);
      
      if (!synthesisResult.success) {
        throw new Error(synthesisResult.message || '语音合成失败');
      }
      
      // 步骤2: 播放语音
      const audio = await this.playSpeechFile(synthesisResult.fileName);
      audio.play();
      
      return audio;
    } catch (error) {
      console.error('合成并播放语音失败:', error);
      throw error;
    }
  }
};

export default speechApi;