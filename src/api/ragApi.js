import axios from 'axios'

// 配置axios基础URL
const API_BASE_URL = 'http://localhost:8081/api'

// 创建axios实例
const ragApi = axios.create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
    timeout: 30000 // 30秒超时
})

/**
 * RAG增强问答 - 同步版本
 * @param {string} message 用户消息
 * @param {string} context 可选的上下文信息
 * @returns {Promise} 返回答案和来源信息
 */
export function ragChat(message, context = '') {
    return ragApi.post('/ai/rag/chat', {
        message,
        context
    })
}

/**
 * RAG增强问答 - 流式版本
 * @param {number} memoryId 聊天室ID
 * @param {string} message 用户消息
 * @param {Function} onMessage 接收消息的回调函数
 * @param {Function} onError 错误处理回调函数
 * @param {Function} onClose 连接关闭回调函数
 * @returns {EventSource} 返回 EventSource 对象，用于手动关闭连接
 */
export function ragChatStream(memoryId, message, onMessage, onError, onClose) {
    // 构建URL参数
    const params = new URLSearchParams({
        memoryId: memoryId,
        message: message
    })
    
    // 创建 EventSource 连接
    const eventSource = new EventSource(`${API_BASE_URL}/ai/rag/chat-stream?${params}`)
    
    // 处理接收到的消息
    eventSource.onmessage = function(event) {
        try {
            const data = event.data
            if (data && data.trim() !== '') {
                onMessage && onMessage(data)
            }
        } catch (error) {
            console.error('解析RAG消息失败:', error)
            onError && onError(error)
        }
    }
    
    // 处理错误
    eventSource.onerror = function(error) {
        console.log('RAG SSE 连接状态:', eventSource.readyState)
        if (eventSource.readyState !== EventSource.CLOSED) {
            console.error('RAG SSE 连接错误:', error)
            onError && onError(error)
        } else {
            console.log('RAG SSE 连接正常结束')
        }
        
        if (eventSource.readyState !== EventSource.CLOSED) {
            eventSource.close()
        }
    }
    
    // 处理连接关闭
    eventSource.onclose = function() {
        console.log('RAG SSE 连接已关闭')
        onClose && onClose()
    }
    
    return eventSource
}

/**
 * 知识库搜索
 * @param {string} query 搜索查询
 * @param {number} maxResults 最大返回结果数，默认5
 * @param {number} minScore 最小相似度分数，默认0.7
 * @returns {Promise} 返回搜索结果
 */
export function searchKnowledge(query, maxResults = 5, minScore = 0.7) {
    return ragApi.post('/ai/rag/search', {
        query,
        maxResults,
        minScore
    })
}

/**
 * 获取知识库统计信息
 * @returns {Promise} 返回知识库统计信息
 */
export function getKnowledgeStats() {
    return ragApi.get('/ai/rag/stats')
}

/**
 * 批量知识库搜索（用于提供搜索建议）
 * @param {Array<string>} queries 多个搜索查询
 * @returns {Promise} 返回批量搜索结果
 */
export async function batchSearchKnowledge(queries) {
    const promises = queries.map(query => 
        searchKnowledge(query, 3, 0.6).catch(error => {
            console.warn(`搜索失败: ${query}`, error)
            return { data: { results: [] } }
        })
    )
    
    const results = await Promise.all(promises)
    return results.map((result, index) => ({
        query: queries[index],
        results: result.data.results || []
    }))
}

// 错误处理拦截器
ragApi.interceptors.response.use(
    response => response,
    error => {
        console.error('RAG API 错误:', error)
        
        if (error.response) {
            // 服务器返回了错误状态码
            const status = error.response.status
            const message = error.response.data?.message || '服务器错误'
            
            switch (status) {
                case 400:
                    throw new Error(`请求参数错误: ${message}`)
                case 401:
                    throw new Error('API密钥错误或已过期')
                case 403:
                    throw new Error('访问被拒绝')
                case 404:
                    throw new Error('API接口不存在')
                case 429:
                    throw new Error('请求过于频繁，请稍后再试')
                case 500:
                    throw new Error(`服务器内部错误: ${message}`)
                default:
                    throw new Error(`网络错误 (${status}): ${message}`)
            }
        } else if (error.request) {
            // 请求已发出，但没有收到响应
            throw new Error('无法连接到服务器，请检查网络连接')
        } else {
            // 其他错误
            throw new Error(`请求失败: ${error.message}`)
        }
    }
)

export default ragApi 