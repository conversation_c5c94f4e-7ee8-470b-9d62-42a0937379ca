/**
 * 英语学习主题配置
 */

export const englishLearningTopics = {
  // 语法学习
  grammar: {
    name: '语法学习',
    icon: '📚',
    color: '#4CAF50',
    description: '系统学习英语语法规则',
    suggestions: [
      '英语时态的用法和区别是什么？',
      '如何正确使用英语定语从句？',
      '英语虚拟语气的使用方法',
      '英语被动语态的构成和用法'
    ]
  },

  // 口语练习
  speaking: {
    name: '口语练习',
    icon: '🗣️',
    color: '#FF9800',
    description: '提高英语口语表达能力',
    suggestions: [
      '如何快速提高英语口语流利度？',
      '英语发音技巧和练习方法',
      '日常英语口语对话场景练习',
      '如何克服英语口语紧张感？'
    ]
  },

  // 听力训练
  listening: {
    name: '听力训练',
    icon: '🎧',
    color: '#2196F3',
    description: '提升英语听力理解能力',
    suggestions: [
      '英语听力训练的有效方法',
      '如何听懂英语新闻和播客？',
      '不同英语口音的识别技巧',
      '英语听力考试的应试策略'
    ]
  },

  // 阅读理解
  reading: {
    name: '阅读理解',
    icon: '📖',
    color: '#9C27B0',
    description: '增强英语阅读理解技能',
    suggestions: [
      '如何提高英语阅读速度？',
      '英语长难句的理解技巧',
      '英语阅读理解的解题方法',
      '如何扩大英语阅读词汇量？'
    ]
  },

  // 写作技巧
  writing: {
    name: '写作技巧',
    icon: '✍️',
    color: '#607D8B',
    description: '掌握英语写作技能',
    suggestions: [
      '英语作文的结构和写作技巧',
      '如何写出优秀的英语议论文？',
      '英语邮件和商务写作格式',
      '英语写作中的常见错误避免'
    ]
  },

  // 词汇扩展
  vocabulary: {
    name: '词汇扩展',
    icon: '📝',
    color: '#E91E63',
    description: '系统扩大英语词汇量',
    suggestions: [
      '高效记忆英语单词的方法',
      '如何记忆英语词根词缀？',
      '英语同义词和近义词辨析',
      '专业领域英语词汇学习'
    ]
  },

  // 考试备考
  examPrep: {
    name: '考试备考',
    icon: '🎯',
    color: '#FF5722',
    description: '各类英语考试备考指导',
    suggestions: [
      '托福雅思考试备考策略',
      '英语四六级考试技巧',
      'GRE/GMAT英语部分备考',
      '商务英语考试准备方法'
    ]
  },

  // 商务英语
  businessEnglish: {
    name: '商务英语',
    icon: '💼',
    color: '#795548',
    description: '职场英语沟通技能',
    suggestions: [
      '商务英语邮件写作技巧',
      '英语会议和演讲技能',
      '国际商务谈判英语',
      '职场英语沟通礼仪'
    ]
  }
}

// 获取所有主题列表
export const getTopicsList = () => {
  return Object.keys(englishLearningTopics).map(key => ({
    key,
    ...englishLearningTopics[key]
  }))
}

// 根据主题获取建议问题
export const getTopicSuggestions = (topicKey) => {
  return englishLearningTopics[topicKey]?.suggestions || []
}

// 获取随机学习建议
export const getRandomSuggestions = (count = 6) => {
  const allSuggestions = []
  Object.values(englishLearningTopics).forEach(topic => {
    allSuggestions.push(...topic.suggestions)
  })
  
  // 随机选择指定数量的建议
  const shuffled = allSuggestions.sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

export default englishLearningTopics