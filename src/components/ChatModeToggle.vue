<template>
  <div class="chat-mode-toggle" :class="{ 'compact': compact }">
    <div class="mode-selector">
      <div class="mode-tabs">
        <button 
          class="mode-tab" 
          :class="{ 'active': currentMode === 'normal' }"
          @click="switchMode('normal')"
        >
          <span class="mode-icon">💬</span>
          <span class="mode-text" v-if="!compact">普通聊天</span>
        </button>
        <button 
          class="mode-tab" 
          :class="{ 'active': currentMode === 'rag' }"
          @click="switchMode('rag')"
        >
          <span class="mode-icon">🧠</span>
          <span class="mode-text" v-if="!compact">知识问答</span>
        </button>
      </div>
      
      <!-- 模式说明 (非紧凑模式下显示) -->
      <div v-if="!compact" class="mode-description">
        <div v-if="currentMode === 'normal'" class="mode-desc normal-desc">
          <span class="desc-icon">💬</span>
          <span class="desc-text">与AI英语导师进行对话，适合日常英语交流和学习指导</span>
        </div>
        <div v-else class="mode-desc rag-desc">
          <span class="desc-icon">🧠</span>
          <span class="desc-text">基于英语学习知识库的专业问答，适合语法学习、口语练习、考试备考</span>
        </div>
      </div>
    </div>
    
    <!-- RAG模式的知识库信息 (非紧凑模式下显示) -->
    <div v-if="currentMode === 'rag' && !compact" class="knowledge-info">
      <div class="knowledge-stats">
        <div class="stats-header">
          <span class="stats-icon">📚</span>
          <span class="stats-title">知识库信息</span>
          <button 
            class="refresh-btn" 
            @click="refreshStats"
            :disabled="statsLoading"
            title="刷新统计信息"
          >
            <span v-if="statsLoading">⏳</span>
            <span v-else>🔄</span>
          </button>
        </div>
        
        <div v-if="knowledgeStats" class="stats-content">
          <div class="stat-item">
            <span class="stat-label">文档数量:</span>
            <span class="stat-value">{{ knowledgeStats.documentCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">存储类型:</span>
            <span class="stat-value">{{ knowledgeStats.storeType }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">嵌入模型:</span>
            <span class="stat-value">{{ knowledgeStats.embeddingModel }}</span>
          </div>
        </div>
        
        <div class="knowledge-topics">
          <div class="topics-title">可咨询主题:</div>
          <div class="topic-tags">
            <span 
              v-for="topic in suggestedTopics" 
              :key="topic"
              class="topic-tag"
              @click="$emit('topic-select', topic)"
            >
              {{ topic }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getKnowledgeStats } from '../api/ragApi.js'

export default {
  name: 'ChatModeToggle',
  props: {
    modelValue: {
      type: String,
      default: 'normal'
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'mode-change', 'topic-select'],
  data() {
    return {
      currentMode: this.modelValue,
      knowledgeStats: null,
      statsLoading: false,
      suggestedTopics: [
        '语法学习',
        '口语练习',
        '听力训练',
        '阅读理解',
        '写作技巧',
        '词汇扩展',
        '考试备考',
        '商务英语'
      ]
    }
  },
  watch: {
    modelValue(newValue) {
      this.currentMode = newValue
    },
    currentMode(newMode) {
      if (newMode === 'rag') {
        this.loadKnowledgeStats()
      }
    }
  },
  mounted() {
    if (this.currentMode === 'rag') {
      this.loadKnowledgeStats()
    }
  },
  methods: {
    switchMode(mode) {
      this.currentMode = mode
      this.$emit('update:modelValue', mode)
      this.$emit('mode-change', mode)
    },
    
    async loadKnowledgeStats() {
      if (this.statsLoading) return
      
      this.statsLoading = true
      try {
        const response = await getKnowledgeStats()
        this.knowledgeStats = response.data
      } catch (error) {
        console.error('获取知识库统计信息失败:', error)
        this.knowledgeStats = {
          documentCount: '未知',
          storeType: '加载失败',
          embeddingModel: 'multilingual-e5-large'
        }
      } finally {
        this.statsLoading = false
      }
    },
    
    refreshStats() {
      this.loadKnowledgeStats()
    }
  }
}
</script>

<style scoped>
.chat-mode-toggle {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.chat-mode-toggle.compact {
  background: transparent;
  border-radius: 6px;
  box-shadow: none;
  margin-bottom: 0;
}

.mode-selector {
  padding: 20px;
}

.compact .mode-selector {
  padding: 0;
}

.mode-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 16px;
}

.compact .mode-tabs {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 2px;
  margin-bottom: 0;
}

.mode-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
}

.compact .mode-tab {
  padding: 6px 12px;
  gap: 4px;
  font-size: 12px;
  border-radius: 4px;
  min-width: 40px;
}

.mode-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.mode-tab.active {
  background: #007bff;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.mode-icon {
  font-size: 16px;
}

.mode-text {
  font-weight: 600;
}

.mode-description {
  margin-bottom: 16px;
}

.mode-desc {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  border-radius: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.normal-desc {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: 1px solid #e1f5fe;
}

.rag-desc {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #7b1fa2;
  border: 1px solid #f8bbd9;
}

.desc-icon {
  font-size: 16px;
}

.desc-text {
  flex: 1;
}

/* 知识库信息样式 */
.knowledge-info {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.knowledge-stats {
  padding: 20px;
}

.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stats-icon {
  font-size: 16px;
  margin-right: 8px;
}

.stats-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.refresh-btn {
  background: none;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.stats-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-size: 12px;
  color: #495057;
  font-weight: 600;
}

.knowledge-topics {
  margin-top: 16px;
}

.topics-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.topic-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e9ecef;
  color: #495057;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.topic-tag:hover {
  background: #007bff;
  color: white;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mode-selector {
    padding: 16px;
  }
  
  .compact .mode-selector {
    padding: 0;
  }
  
  .mode-tab {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .compact .mode-tab {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 32px;
  }
  
  .mode-icon {
    font-size: 14px;
  }
  
  .compact .mode-icon {
    font-size: 12px;
  }
  
  .stats-content {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .knowledge-stats {
    padding: 16px;
  }
  
  .topic-tag {
    font-size: 10px;
    padding: 3px 6px;
  }
}
</style> 