<template>
  <div class="rag-message" :class="{ 'user-message': isUser, 'ai-message': !isUser }">
    <div class="message-avatar">
      <div class="avatar" :class="{ 'user-avatar': isUser, 'ai-avatar': !isUser, 'rag-avatar': !isUser }">
        {{ isUser ? '我' : '🧠' }}
      </div>
    </div>
    <div class="message-content">
      <div class="message-bubble" :class="{ 'rag-bubble': !isUser }">
        <!-- 用户消息使用普通文本 -->
        <pre v-if="isUser" class="message-text">{{ message }}</pre>
        <!-- AI回复使用Markdown渲染 -->
        <div v-else>
          <div class="message-markdown" v-html="renderedMessage"></div>
          
          <!-- 语音播放和PDF导出功能 -->
          <div class="message-actions">
            <div class="action-buttons">
              <SpeechPlayer 
                :text="message"
                :title="'AI回复'"
                :show-settings="false"
                :auto-play="false"
                @synthesis-complete="onSpeechSynthesisComplete"
                @synthesis-error="onSpeechSynthesisError"
              />
              <button 
                class="pdf-export-btn" 
                @click="exportToPdf"
                :disabled="isExporting"
                :title="isExporting ? 'PDF生成中...' : '导出为PDF'"
              >
                <span v-if="!isExporting">📄</span>
                <span v-else class="loading-spinner">⏳</span>
                {{ isExporting ? '生成中...' : 'PDF' }}
              </button>
            </div>
          </div>
          
          <!-- RAG知识源显示 -->
          <div v-if="sources && sources.length > 0" class="knowledge-sources">
            <div class="sources-header">
              <span class="sources-icon">📚</span>
              <span class="sources-title">学习资料来源 ({{ sources.length }})</span>
              <button 
                class="sources-toggle" 
                @click="toggleSources"
                :class="{ 'expanded': showSources }"
              >
                {{ showSources ? '收起' : '展开' }}
              </button>
            </div>
            
            <div v-if="showSources" class="sources-list">
              <div 
                v-for="(source, index) in sources" 
                :key="index" 
                class="source-item"
                @click="toggleSourceDetail(index)"
              >
                <div class="source-header">
                  <span class="source-icon">📄</span>
                  <span class="source-filename">{{ source.fileName || `来源 ${index + 1}` }}</span>
                  <span v-if="source.score" class="source-score">
                    相似度: {{ (source.score * 100).toFixed(1) }}%
                  </span>
                </div>
                
                <div v-if="expandedSources[index]" class="source-content">
                  <div class="source-text">
                    {{ truncateText(source.text, 200) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="message-time">{{ formatTime(timestamp) }}</div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '../utils/index.js'
import { marked } from 'marked'
import SpeechPlayer from './SpeechPlayer.vue'
import { exportAndDownloadPdf } from '../api/pdfApi.js'

export default {
  name: 'RagMessage',
  components: {
    SpeechPlayer
  },
  props: {
    message: {
      type: String,
      required: true
    },
    isUser: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: Date,
      default: () => new Date()
    },
    sources: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      showSources: false,
      expandedSources: {},
      isExporting: false
    }
  },
  computed: {
    renderedMessage() {
      if (this.isUser) {
        return this.message
      }
      // 配置marked选项
      marked.setOptions({
        breaks: true,
        gfm: true,
        sanitize: false,
        highlight: function(code, lang) {
          return code
        }
      })
      return marked(this.message)
    }
  },
  methods: {
    formatTime,
    
    toggleSources() {
      this.showSources = !this.showSources
    },
    
    toggleSourceDetail(index) {
      this.$set(this.expandedSources, index, !this.expandedSources[index])
    },
    
    truncateText(text, maxLength) {
      if (!text) return ''
      if (text.length <= maxLength) return text
      return text.substring(0, maxLength) + '...'
    },
    
    // 语音合成相关方法
    onSpeechSynthesisComplete(result) {
      console.log('语音合成完成:', result);
      this.$emit('speech-synthesis-complete', result);
    },
    
    onSpeechSynthesisError(error) {
      console.error('语音合成失败:', error);
      this.$emit('speech-synthesis-error', error);
    },
    
    // PDF导出方法
    async exportToPdf() {
      if (this.isExporting) {
        return;
      }
      
      try {
        this.isExporting = true;
        
        // 生成包含来源信息的完整内容
        let fullContent = this.message;
        
        if (this.sources && this.sources.length > 0) {
          fullContent += '\n\n---\n\n## 参考资料\n\n';
          this.sources.forEach((source, index) => {
            fullContent += `### ${index + 1}. ${source.fileName || `来源 ${index + 1}`}\n`;
            if (source.score) {
              fullContent += `相似度: ${(source.score * 100).toFixed(1)}%\n\n`;
            }
            fullContent += `${source.text}\n\n`;
          });
        }
        
        // 生成PDF标题
        const title = this.generatePdfTitle();
        
        // 调用PDF导出API
        await exportAndDownloadPdf(fullContent, title);
        
        // 发出成功事件
        this.$emit('pdf-export-success', {
          title: title,
          timestamp: new Date(),
          includedSources: this.sources ? this.sources.length : 0
        });
        
        console.log('RAG消息PDF导出成功，包含来源数量:', this.sources ? this.sources.length : 0);
        
      } catch (error) {
        console.error('PDF导出失败:', error);
        
        // 发出失败事件
        this.$emit('pdf-export-error', {
          error: error.message || 'PDF导出失败',
          timestamp: new Date()
        });
        
        alert('PDF导出失败，请稍后重试');
        
      } finally {
        this.isExporting = false;
      }
    },
    
    // 生成PDF标题
    generatePdfTitle() {
      // 从消息内容中提取前20个字符作为标题
      let title = this.message.replace(/[#*`\n\r]/g, '').trim();
      
      if (title.length > 20) {
        title = title.substring(0, 20) + '...';
      }
      
      if (!title) {
        title = 'RAG知识问答';
      }
      
      return title;
    }
  }
}
</script>

<style scoped>
.rag-message {
  display: flex;
  margin-bottom: 20px;
  padding: 0 20px;
}

.user-message {
  justify-content: flex-end;
  flex-direction: row;
}

.user-message .message-avatar {
  order: 2;
}

.user-message .message-content {
  order: 1;
}

.ai-message {
  justify-content: flex-start;
  flex-direction: row;
}

.ai-message .message-avatar {
  order: 1;
}

.ai-message .message-content {
  order: 2;
}

.message-avatar {
  display: flex;
  align-items: flex-start;
  margin: 0 10px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.user-avatar {
  background-color: #007bff;
}

.ai-avatar {
  background-color: #6c757d;
}

.rag-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-size: 16px;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-bubble {
  background-color: #f8f9fa;
  border-radius: 18px;
  padding: 12px 16px;
  position: relative;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.user-message .message-bubble {
  background-color: #007bff;
  color: white;
}

.rag-bubble {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e9ecef;
}

.message-text {
  margin: 0;
  white-space: pre-wrap;
  font-family: inherit;
}

.message-markdown {
  line-height: 1.6;
}

.message-markdown :deep(h1),
.message-markdown :deep(h2),
.message-markdown :deep(h3),
.message-markdown :deep(h4),
.message-markdown :deep(h5),
.message-markdown :deep(h6) {
  margin: 10px 0 8px 0;
  color: #2c3e50;
}

.message-markdown :deep(p) {
  margin: 8px 0;
}

.message-markdown :deep(code) {
  background-color: #f1f3f4;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 90%;
}

.message-markdown :deep(pre) {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
}

.message-markdown :deep(pre code) {
  background: none;
  padding: 0;
}

.message-markdown :deep(ul),
.message-markdown :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.message-markdown :deep(li) {
  margin: 4px 0;
}

.message-markdown :deep(blockquote) {
  border-left: 4px solid #007bff;
  padding-left: 12px;
  margin: 8px 0;
  color: #6c757d;
}

.message-markdown :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.message-markdown :deep(th),
.message-markdown :deep(td) {
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  text-align: left;
}

.message-markdown :deep(th) {
  background-color: #f8f9fa;
  font-weight: bold;
}

.message-time {
  font-size: 12px;
  color: #6c757d;
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

/* 知识源样式 */
.knowledge-sources {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #dee2e6;
}

.sources-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 8px 0;
}

.sources-icon {
  font-size: 16px;
  margin-right: 6px;
}

.sources-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  flex: 1;
}

.sources-toggle {
  background: none;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sources-toggle:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
}

.sources-toggle.expanded {
  background-color: #e9ecef;
  color: #495057;
}

.sources-list {
  margin-top: 8px;
}

.source-item {
  background-color: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
}

.source-item:hover {
  border-color: #ced4da;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.source-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  background-color: #f8f9fa;
}

.source-icon {
  font-size: 14px;
  margin-right: 8px;
}

.source-filename {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
  color: #495057;
}

.source-score {
  font-size: 12px;
  color: #6c757d;
  background-color: #e9ecef;
  padding: 2px 6px;
  border-radius: 10px;
}

.source-content {
  padding: 12px;
  border-top: 1px solid #e9ecef;
}

.source-text {
  font-size: 13px;
  line-height: 1.5;
  color: #6c757d;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

/* 消息操作样式 */
.message-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* PDF导出按钮样式 */
.pdf-export-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pdf-export-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.pdf-export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.pdf-export-btn .loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }
  
  .rag-message {
    padding: 0 10px;
  }
  
  .sources-header {
    font-size: 13px;
  }
  
  .source-item {
    margin-bottom: 6px;
  }
}
</style> 