<template>
  <div class="sidebar" :class="{ 'collapsed': isCollapsed }">
    <!-- 侧栏头部 -->
    <div class="sidebar-header">
      <div class="sidebar-title" v-if="!isCollapsed">
        <span class="title-icon">📚</span>
        <span class="title-text">知识库</span>
      </div>
      <button 
        class="collapse-btn"
        @click="toggleCollapse"
        :title="isCollapsed ? '展开侧栏' : '收起侧栏'"
      >
        {{ isCollapsed ? '▶' : '◀' }}
      </button>
    </div>

    <!-- 侧栏内容 -->
    <div class="sidebar-content" v-if="!isCollapsed">
      <!-- 知识库统计 -->
      <div class="stats-section">
        <div class="stats-header">
          <span class="stats-title">知识库信息</span>
          <button 
            class="refresh-btn"
            @click="refreshStats"
            :disabled="statsLoading"
            :title="statsLoading ? '正在加载...' : '刷新统计信息'"
          >
            <span class="refresh-icon" :class="{ 'spinning': statsLoading }">🔄</span>
          </button>
        </div>
        <div class="stat-item">
          <span class="stat-label">文档数量:</span>
          <span class="stat-value" :class="{ 'error': statsError }">{{ stats.documentCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">存储类型:</span>
          <span class="stat-value" :class="{ 'error': statsError }">{{ stats.storageType }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">嵌入模型:</span>
          <span class="stat-value" :class="{ 'error': statsError }">{{ stats.embeddingModel }}</span>
        </div>
      </div>

      <!-- 学习主题标签 -->
      <div class="topics-section">
        <div class="section-title">
          <span class="section-icon">🎯</span>
          <span>学习主题</span>
        </div>
        <div class="topics-grid">
          <button 
            v-for="topic in topics"
            :key="topic"
            class="topic-tag"
            @click="$emit('topic-select', topic)"
          >
            {{ topic }}
          </button>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="actions-section">
        <div class="section-title">
          <span class="section-icon">⚡</span>
          <span>快速操作</span>
        </div>
        <div class="quick-actions">
          <button class="action-btn" @click="$emit('clear-chat')">
            <span class="action-icon">🗑️</span>
            <span>清空对话</span>
          </button>
          <button class="action-btn" @click="$emit('export-chat')">
            <span class="action-icon">📄</span>
            <span>导出对话</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 收起状态下的简化显示 -->
    <div class="sidebar-collapsed" v-else>
      <div class="collapsed-stats">
        <div class="collapsed-stat" :title="`文档数量: ${stats.documentCount}`">
          📚 {{ stats.documentCount }}
        </div>
      </div>
      <div class="collapsed-topics">
        <button 
          v-for="(topic, index) in topics.slice(0, 3)"
          :key="topic"
          class="collapsed-topic"
          @click="$emit('topic-select', topic)"
          :title="topic"
        >
          {{ getTopicEmoji(index) }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { getKnowledgeStats } from '../api/ragApi.js'

export default {
  name: 'Sidebar',
  props: {
    topics: {
      type: Array,
      default: () => ['语法学习', '口语练习', '听力训练', '阅读理解', '写作技巧', '词汇扩展', '考试备考', '商务英语']
    }
  },
  emits: ['topic-select', 'clear-chat', 'export-chat'],
  data() {
    return {
      isCollapsed: false,
      stats: {
        documentCount: '加载中...',
        storageType: '加载中...',
        embeddingModel: '加载中...'
      },
      statsLoading: false,
      statsError: false
    }
  },
  mounted() {
    this.loadKnowledgeStats()
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      this.$emit('sidebar-toggle', this.isCollapsed)
    },
    getTopicEmoji(index) {
      const emojis = ['📚', '💬', '🎧', '📖', '✍️', '📝', '📋', '💼']
      return emojis[index] || '📚'
    },
    async loadKnowledgeStats() {
      if (this.statsLoading) return
      
      this.statsLoading = true
      this.statsError = false
      
      try {
        const response = await getKnowledgeStats()
        this.stats = {
          documentCount: response.data.documentCount || '未知',
          storageType: response.data.storeType || response.data.storageType || 'PostgreSQL 向量数据库',
          embeddingModel: response.data.embeddingModel || 'multilingual-e5-large'
        }
      } catch (error) {
        console.error('获取知识库统计信息失败:', error)
        this.statsError = true
        this.stats = {
          documentCount: '获取失败',
          storageType: 'PostgreSQL 向量数据库',
          embeddingModel: 'LocalEmbeddingModel'
        }
      } finally {
        this.statsLoading = false
      }
    },
    refreshStats() {
      this.loadKnowledgeStats()
    }
  }
}
</script>

<style scoped>
.sidebar {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  z-index: 10;
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-bottom: 1px solid #e1e5e9;
  min-height: 60px;
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 18px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.collapse-btn {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  color: #6c757d;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.stats-section,
.topics-section,
.actions-section {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.stats-section {
  background: #f8f9fa;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.stats-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.refresh-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.1);
}

.refresh-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.refresh-icon {
  font-size: 12px;
  display: inline-block;
  transition: transform 0.3s ease;
}

.refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.stat-label {
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  color: #495057;
  font-weight: 600;
  max-width: 150px;
  text-align: right;
  word-break: break-word;
  transition: color 0.2s ease;
}

.stat-value.error {
  color: #dc3545;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.section-icon {
  font-size: 14px;
}

.topics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.topic-tag {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 6px;
  padding: 6px 8px;
  font-size: 11px;
  color: #1976d2;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.topic-tag:hover {
  background: #bbdefb;
  border-color: #90caf9;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #495057;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
}

.action-icon {
  font-size: 14px;
}

/* 收起状态样式 */
.sidebar-collapsed {
  padding: 15px 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.collapsed-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.collapsed-stat {
  font-size: 10px;
  color: #6c757d;
  text-align: center;
  line-height: 1.2;
}

.collapsed-topics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.collapsed-topic {
  width: 36px;
  height: 36px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
}

.collapsed-topic:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #ced4da;
}

@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }
  
  .sidebar.collapsed {
    width: 50px;
  }
  
  .topics-grid {
    grid-template-columns: 1fr;
  }
  
  .collapsed-topic {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>