<template>
  <teleport to="body" v-if="show">
    <div class="speech-settings-overlay" @click="handleOverlayClick">
      <div class="speech-settings-modal" @click.stop>
        <div class="settings-header">
      <h3 class="settings-title">🎤 语音设置</h3>
      <button @click="closeSettings" class="close-btn">×</button>
    </div>
    
    <div class="settings-content">
      <!-- 语音选择 -->
      <div class="setting-item">
        <label class="setting-label">默认语音</label>
        <select v-model="selectedVoice" @change="onVoiceChange" class="setting-select">
          <option value="">选择语音类型</option>
          <option 
            v-for="(desc, voice) in availableVoices" 
            :key="voice" 
            :value="voice"
          >
            {{ desc }}
          </option>
        </select>
        <div class="setting-description">选择您喜欢的语音类型，将应用于所有语音合成</div>
      </div>

      <!-- 自动播放设置 -->
      <div class="setting-item">
        <label class="setting-label">
          <input 
            type="checkbox" 
            v-model="autoPlayEnabled" 
            @change="onAutoPlayChange"
          />
          自动播放语音
        </label>
        <div class="setting-description">AI回复后自动生成并播放语音</div>
      </div>

      <!-- 语音质量设置 -->
      <div class="setting-item">
        <label class="setting-label">语音质量</label>
        <select v-model="voiceQuality" @change="onQualityChange" class="setting-select">
          <option value="standard">标准质量</option>
          <option value="high">高质量</option>
        </select>
        <div class="setting-description">高质量语音生成时间较长但效果更好</div>
      </div>

      <!-- 语音速度设置 -->
      <div class="setting-item">
        <label class="setting-label">语音速度</label>
        <div class="speed-control">
          <input 
            type="range" 
            min="0.5" 
            max="2" 
            step="0.1" 
            v-model="speechRate"
            @input="onSpeedChange"
            class="speed-slider"
          />
          <span class="speed-value">{{ speechRate }}x</span>
        </div>
        <div class="setting-description">调整语音播放速度 (0.5x - 2.0x)</div>
      </div>

      <!-- 语音文件管理 -->
      <div class="setting-item">
        <label class="setting-label">语音文件管理</label>
        <div class="file-management">
          <button @click="showFileManager = !showFileManager" class="management-btn">
            {{ showFileManager ? '隐藏' : '查看' }}语音文件
          </button>
          <button @click="clearAllFiles" class="management-btn danger">清理所有文件</button>
        </div>
        
        <!-- 文件列表 -->
        <div v-if="showFileManager" class="file-list">
          <div v-if="isLoadingFiles" class="loading">正在加载文件列表...</div>
          <div v-else-if="speechFiles.length === 0" class="no-files">暂无语音文件</div>
          <div v-else>
            <div 
              v-for="file in speechFiles" 
              :key="file.name"
              class="file-item"
            >
              <div class="file-info">
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-time">{{ formatTime(file.lastModified) }}</span>
              </div>
              <div class="file-actions">
                <button @click="playFile(file.name)" class="file-btn play">播放</button>
                <button @click="downloadFile(file.name)" class="file-btn download">下载</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 测试语音 -->
      <div class="setting-item">
        <label class="setting-label">测试语音</label>
        <div class="test-section">
          <textarea 
            v-model="testText" 
            placeholder="输入要测试的文本..."
            class="test-textarea"
            rows="3"
          ></textarea>
          <button 
            @click="testSpeech" 
            :disabled="!testText.trim() || isTesting"
            class="test-btn"
          >
            {{ isTesting ? '正在生成...' : '测试语音' }}
          </button>
        </div>
      </div>
    </div>

        <!-- 操作按钮 -->
        <div class="settings-footer">
          <button @click="resetToDefaults" class="footer-btn secondary">恢复默认</button>
          <button @click="saveSettings" class="footer-btn primary">保存设置</button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script>
import { speechApi } from '../api/speechApi.js';

export default {
  name: 'SpeechSettings',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedVoice: '',
      availableVoices: {},
      autoPlayEnabled: false,
      voiceQuality: 'standard',
      speechRate: 1.0,
      showFileManager: false,
      speechFiles: [],
      isLoadingFiles: false,
      testText: '你好，这是一个语音测试。Hello, this is a speech test.',
      isTesting: false,
      
      // 默认设置
      defaultSettings: {
        selectedVoice: 'zh-CN-XiaoxiaoNeural',
        autoPlayEnabled: false,
        voiceQuality: 'standard',
        speechRate: 1.0
      }
    };
  },
  async mounted() {
    await this.loadSettings();
    await this.loadVoices();
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.loadSpeechFiles();
      }
    }
  },
  methods: {
    async loadVoices() {
      try {
        const result = await speechApi.getSupportedVoices();
        if (result.success) {
          this.availableVoices = result.voices;
          if (!this.selectedVoice) {
            this.selectedVoice = result.defaultVoice || '';
          }
        }
      } catch (error) {
        console.error('加载语音列表失败:', error);
      }
    },

    async loadSpeechFiles() {
      this.isLoadingFiles = true;
      try {
        const result = await speechApi.getSpeechFiles();
        if (result.success) {
          this.speechFiles = result.files || [];
        }
      } catch (error) {
        console.error('加载语音文件列表失败:', error);
      } finally {
        this.isLoadingFiles = false;
      }
    },

    loadSettings() {
      const savedSettings = localStorage.getItem('speechSettings');
      if (savedSettings) {
        try {
          const settings = JSON.parse(savedSettings);
          Object.assign(this, settings);
        } catch (error) {
          console.error('加载语音设置失败:', error);
        }
      }
    },

    saveSettings() {
      const settings = {
        selectedVoice: this.selectedVoice,
        autoPlayEnabled: this.autoPlayEnabled,
        voiceQuality: this.voiceQuality,
        speechRate: this.speechRate
      };
      
      localStorage.setItem('speechSettings', JSON.stringify(settings));
      this.$emit('settings-changed', settings);
      this.$emit('close');
      
      // 显示保存成功提示
      this.showNotification('设置已保存');
    },

    resetToDefaults() {
      Object.assign(this, this.defaultSettings);
      this.showNotification('已恢复默认设置');
    },

    onVoiceChange() {
      this.$emit('voice-changed', this.selectedVoice);
    },

    onAutoPlayChange() {
      this.$emit('auto-play-changed', this.autoPlayEnabled);
    },

    onQualityChange() {
      this.$emit('quality-changed', this.voiceQuality);
    },

    onSpeedChange() {
      this.$emit('speed-changed', this.speechRate);
    },

    async testSpeech() {
      if (!this.testText.trim()) return;
      
      this.isTesting = true;
      try {
        const voice = this.selectedVoice || null;
        await speechApi.synthesizeAndPlay(this.testText, voice, '语音测试');
        this.showNotification('语音测试完成');
      } catch (error) {
        console.error('语音测试失败:', error);
        this.showNotification('语音测试失败，请检查设置', 'error');
      } finally {
        this.isTesting = false;
      }
    },

    async playFile(fileName) {
      try {
        const audio = await speechApi.playSpeechFile(fileName);
        audio.play();
      } catch (error) {
        console.error('播放文件失败:', error);
        this.showNotification('播放失败', 'error');
      }
    },

    async downloadFile(fileName) {
      try {
        await speechApi.downloadSpeechFile(fileName);
        this.showNotification('下载完成');
      } catch (error) {
        console.error('下载文件失败:', error);
        this.showNotification('下载失败', 'error');
      }
    },

    async clearAllFiles() {
      if (!confirm('确定要清理所有语音文件吗？此操作不可撤销。')) {
        return;
      }
      
      // 这里需要后端支持批量删除API
      this.showNotification('清理功能正在开发中');
    },

    closeSettings() {
      this.$emit('close');
    },
    
    handleOverlayClick() {
      this.closeSettings();
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleString();
    },

    showNotification(message, type = 'success') {
      // 简单的通知实现，可以替换为更好的通知组件
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.textContent = message;
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: ${type === 'error' ? '#ff4444' : '#4CAF50'};
        color: white;
        border-radius: 6px;
        z-index: 3000;
        animation: slideIn 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        font-weight: 500;
      `;
      
      document.body.appendChild(notification);
      
      setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 300);
      }, 3000);
    }
  }
};
</script>

<style scoped>
.speech-settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
  backdrop-filter: blur(2px);
  box-sizing: border-box;
}

.speech-settings-modal {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.settings-title {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.settings-content {
  padding: 0 24px;
}

.setting-item {
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}

.setting-label input[type="checkbox"] {
  margin-right: 8px;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.setting-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.speed-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speed-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #ddd;
  outline: none;
  appearance: none;
}

.speed-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #4CAF50;
  cursor: pointer;
}

.speed-value {
  font-weight: 500;
  min-width: 40px;
  text-align: center;
  color: #333;
}

.file-management {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.management-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.management-btn:hover {
  background: #f5f5f5;
}

.management-btn.danger {
  border-color: #ff4444;
  color: #ff4444;
}

.management-btn.danger:hover {
  background: #ff4444;
  color: white;
}

.file-list {
  margin-top: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  display: block;
  font-weight: 500;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size,
.file-time {
  font-size: 10px;
  color: #666;
  margin-right: 8px;
}

.file-actions {
  display: flex;
  gap: 4px;
}

.file-btn {
  padding: 2px 6px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: white;
  cursor: pointer;
  font-size: 10px;
}

.file-btn:hover {
  background: #f5f5f5;
}

.file-btn.play {
  border-color: #4CAF50;
  color: #4CAF50;
}

.file-btn.download {
  border-color: #2196F3;
  color: #2196F3;
}

.test-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 60px;
}

.test-btn {
  align-self: flex-start;
  padding: 6px 16px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.test-btn:hover:not(:disabled) {
  background: #45a049;
}

.settings-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
}

.footer-btn {
  padding: 8px 20px;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.footer-btn.secondary {
  background: white;
  color: #666;
}

.footer-btn.secondary:hover {
  background: #f5f5f5;
}

.footer-btn.primary {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.footer-btn.primary:hover {
  background: #45a049;
}

.loading,
.no-files {
  text-align: center;
  color: #666;
  font-size: 12px;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .speech-settings-overlay {
    padding: 10px;
    align-items: flex-start;
    padding-top: 20px;
  }
  
  .speech-settings-modal {
    max-width: 100%;
    border-radius: 8px;
    max-height: calc(100vh - 40px);
  }
  
  .settings-header,
  .settings-content,
  .settings-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  .settings-header {
    padding-top: 16px;
    padding-bottom: 16px;
  }
  
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .file-actions {
    align-self: flex-end;
  }
  
  .file-management {
    flex-direction: column;
    align-items: stretch;
  }
  
  .management-btn {
    text-align: center;
  }
}

/* 动画 */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>