<template>
  <div class="speech-player">
    <!-- 语音合成按钮 -->
    <div class="speech-actions">
      <button 
        v-if="!isLoading && !audioElement"
        @click="handleSynthesize"
        class="speech-btn synthesize-btn"
        :disabled="!text || isLoading"
        title="将文本转换为语音"
      >
        <span class="icon">🎤</span>
        <span class="text">生成语音</span>
      </button>

      <!-- 语音播放控制 -->
      <div v-if="audioElement" class="audio-controls">
        <button 
          @click="togglePlay" 
          class="speech-btn play-btn"
          :class="{ playing: isPlaying }"
          :title="isPlaying ? '暂停播放' : '播放语音'"
        >
          <span class="icon">{{ isPlaying ? '⏸️' : '▶️' }}</span>
          <span class="text">{{ isPlaying ? '暂停' : '播放' }}</span>
        </button>
        
        <button 
          @click="stopAudio" 
          class="speech-btn stop-btn"
          title="停止播放"
        >
          <span class="icon">⏹️</span>
          <span class="text">停止</span>
        </button>

        <div class="audio-progress">
          <div class="progress-bar">
            <div 
              class="progress-fill" 
              :style="{ width: progressPercentage + '%' }"
            ></div>
          </div>
          <span class="time-display">{{ formatTime(currentTime) }} / {{ formatTime(duration) }}</span>
        </div>
      </div>

      <!-- 下载按钮 -->
      <button 
        v-if="fileName"
        @click="downloadAudio"
        class="speech-btn download-btn"
        title="下载语音文件"
      >
        <span class="icon">💾</span>
        <span class="text">下载</span>
      </button>

      <!-- 语音设置 -->
      <div v-if="showSettings" class="voice-settings">
        <select v-model="selectedVoice" @change="onVoiceChange" class="voice-select">
          <option value="">选择语音</option>
          <option 
            v-for="(desc, voice) in availableVoices" 
            :key="voice" 
            :value="voice"
          >
            {{ desc }}
          </option>
        </select>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <span class="loading-text">{{ loadingText }}</span>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-message">
      <span class="error-icon">⚠️</span>
      <span class="error-text">{{ error }}</span>
      <button @click="clearError" class="error-close">×</button>
    </div>
  </div>
</template>

<script>
import { speechApi } from '../api/speechApi.js';

export default {
  name: 'SpeechPlayer',
  props: {
    text: {
      type: String,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    autoPlay: {
      type: Boolean,
      default: false
    },
    showSettings: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      audioElement: null,
      isPlaying: false,
      isLoading: false,
      loadingText: '正在生成语音...',
      fileName: null,
      error: null,
      currentTime: 0,
      duration: 0,
      selectedVoice: '',
      availableVoices: {}
    };
  },
  computed: {
    progressPercentage() {
      if (!this.duration) return 0;
      return (this.currentTime / this.duration) * 100;
    }
  },
  async mounted() {
    await this.loadVoices();
    if (this.autoPlay && this.text) {
      this.handleSynthesize();
    }
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    async loadVoices() {
      try {
        const result = await speechApi.getSupportedVoices();
        if (result.success) {
          this.availableVoices = result.voices;
          this.selectedVoice = result.defaultVoice || '';
        }
      } catch (error) {
        console.error('加载语音列表失败:', error);
      }
    },

    async handleSynthesize() {
      if (!this.text.trim()) {
        this.error = '请输入要转换的文本内容';
        return;
      }

      this.isLoading = true;
      this.loadingText = '正在生成语音...';
      this.error = null;

      try {
        // 合成语音
        const result = this.selectedVoice 
          ? await speechApi.synthesizeWithVoice(this.text, this.selectedVoice, this.title)
          : await speechApi.synthesizeText(this.text, this.title);

        if (!result.success) {
          throw new Error(result.message || '语音合成失败');
        }

        this.fileName = result.fileName;
        this.loadingText = '正在加载音频...';

        // 创建音频元素
        this.audioElement = await speechApi.playSpeechFile(result.fileName);
        this.setupAudioEvents();

        // 如果设置了自动播放
        if (this.autoPlay) {
          this.audioElement.play();
        }

        this.$emit('synthesis-complete', result);
        
      } catch (error) {
        this.error = error.message || '语音合成失败，请重试';
        this.$emit('synthesis-error', error);
      } finally {
        this.isLoading = false;
      }
    },

    setupAudioEvents() {
      if (!this.audioElement) return;

      this.audioElement.addEventListener('play', () => {
        this.isPlaying = true;
        this.$emit('play-start');
      });

      this.audioElement.addEventListener('pause', () => {
        this.isPlaying = false;
        this.$emit('play-pause');
      });

      this.audioElement.addEventListener('ended', () => {
        this.isPlaying = false;
        this.currentTime = 0;
        this.$emit('play-end');
      });

      this.audioElement.addEventListener('timeupdate', () => {
        this.currentTime = this.audioElement.currentTime;
      });

      this.audioElement.addEventListener('loadedmetadata', () => {
        this.duration = this.audioElement.duration;
      });

      this.audioElement.addEventListener('error', (error) => {
        this.error = '音频播放失败';
        this.isPlaying = false;
        this.$emit('play-error', error);
      });
    },

    togglePlay() {
      if (!this.audioElement) return;

      if (this.isPlaying) {
        this.audioElement.pause();
      } else {
        this.audioElement.play();
      }
    },

    stopAudio() {
      if (!this.audioElement) return;
      
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
      this.isPlaying = false;
    },

    async downloadAudio() {
      if (!this.fileName) return;

      try {
        const response = await speechApi.downloadSpeechFile(this.fileName);
        
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.download = this.fileName;
        link.click();
        
        // 清理URL
        window.URL.revokeObjectURL(url);
        
        this.$emit('download-complete', this.fileName);
      } catch (error) {
        this.error = '下载失败，请重试';
        this.$emit('download-error', error);
      }
    },

    onVoiceChange() {
      // 如果已经有音频，清理并重新生成
      if (this.audioElement) {
        this.cleanup();
        if (this.text) {
          this.handleSynthesize();
        }
      }
    },

    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '0:00';
      
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    clearError() {
      this.error = null;
    },

    cleanup() {
      if (this.audioElement) {
        this.audioElement.pause();
        this.audioElement.removeEventListener('play', () => {});
        this.audioElement.removeEventListener('pause', () => {});
        this.audioElement.removeEventListener('ended', () => {});
        this.audioElement.removeEventListener('timeupdate', () => {});
        this.audioElement.removeEventListener('loadedmetadata', () => {});
        this.audioElement.removeEventListener('error', () => {});
        this.audioElement = null;
      }
      
      this.isPlaying = false;
      this.currentTime = 0;
      this.duration = 0;
      this.fileName = null;
    },

    // 公共方法，供父组件调用
    synthesize() {
      this.handleSynthesize();
    },

    play() {
      if (this.audioElement) {
        this.audioElement.play();
      } else if (this.text) {
        this.handleSynthesize();
      }
    },

    pause() {
      if (this.audioElement) {
        this.audioElement.pause();
      }
    },

    stop() {
      this.stopAudio();
    }
  }
};
</script>

<style scoped>
.speech-player {
  margin: 8px 0;
}

.speech-actions {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.speech-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #fff;
  color: #333;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.speech-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #ccc;
}

.speech-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.synthesize-btn {
  background: #4CAF50;
  color: white;
  border-color: #4CAF50;
}

.synthesize-btn:hover:not(:disabled) {
  background: #45a049;
}

.play-btn.playing {
  background: #ff9800;
  color: white;
  border-color: #ff9800;
}

.stop-btn {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.stop-btn:hover:not(:disabled) {
  background: #da190b;
}

.download-btn {
  background: #2196F3;
  color: white;
  border-color: #2196F3;
}

.download-btn:hover:not(:disabled) {
  background: #0b7dda;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.audio-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 150px;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4CAF50;
  transition: width 0.1s ease;
}

.time-display {
  font-size: 11px;
  color: #666;
  white-space: nowrap;
}

.voice-settings {
  margin-left: auto;
}

.voice-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.loading-state {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f8ff;
  border: 1px solid #b3d9ff;
  border-radius: 6px;
  font-size: 12px;
  color: #0066cc;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  font-size: 12px;
  color: #c62828;
  margin-top: 8px;
}

.error-close {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #c62828;
  padding: 0 4px;
}

.error-close:hover {
  background: #ffcdd2;
  border-radius: 2px;
}

.icon {
  font-size: 14px;
}

.text {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .speech-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .audio-controls {
    justify-content: space-between;
  }
  
  .audio-progress {
    min-width: auto;
    flex: 1;
  }
  
  .voice-settings {
    margin-left: 0;
  }
}
</style>