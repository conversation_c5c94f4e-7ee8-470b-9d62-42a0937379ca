<template>
  <div class="chat-message" :class="{ 'user-message': isUser, 'ai-message': !isUser }">
    <div class="message-avatar">
      <div class="avatar" :class="{ 'user-avatar': isUser, 'ai-avatar': !isUser }">
        {{ isUser ? '我' : 'AI' }}
      </div>
    </div>
    <div class="message-content">
      <div class="message-bubble">
        <!-- 用户消息使用普通文本 -->
        <pre v-if="isUser" class="message-text">{{ message }}</pre>
        <!-- AI回复使用Markdown渲染 -->
        <div v-else>
          <div class="message-markdown" v-html="renderedMessage"></div>
          
          <!-- 语音播放和PDF导出功能 -->
          <div class="message-actions">
            <div class="action-buttons">
              <SpeechPlayer 
                :text="message"
                :title="'AI回复'"
                :show-settings="false"
                :auto-play="false"
                @synthesis-complete="onSpeechSynthesisComplete"
                @synthesis-error="onSpeechSynthesisError"
              />
              <button 
                class="pdf-export-btn" 
                @click="exportToPdf"
                :disabled="isExporting"
                :title="isExporting ? 'PDF生成中...' : '导出为PDF'"
              >
                <span v-if="!isExporting">📄</span>
                <span v-else class="loading-spinner">⏳</span>
                {{ isExporting ? '生成中...' : 'PDF' }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="message-time">{{ formatTime(timestamp) }}</div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '../utils/index.js'
import { marked } from 'marked'
import SpeechPlayer from './SpeechPlayer.vue'
import { exportAndDownloadPdf } from '../api/pdfApi.js'

export default {
  name: 'ChatMessage',
  components: {
    SpeechPlayer
  },
  props: {
    message: {
      type: String,
      required: true
    },
    isUser: {
      type: Boolean,
      default: false
    },
    timestamp: {
      type: Date,
      default: () => new Date()
    }
  },
  data() {
    return {
      isExporting: false
    }
  },
  computed: {
    renderedMessage() {
      if (this.isUser) {
        return this.message
      }
      // 配置marked选项
      marked.setOptions({
        breaks: true, // 支持换行
        gfm: true, // 支持GitHub风格的Markdown
        sanitize: false, // 不过滤HTML（根据需要可以开启）
        highlight: function(code, lang) {
          // 可以在这里添加代码高亮功能
          return code
        }
      })
      return marked(this.message)
    }
  },
  methods: {
    formatTime,
    
    // 语音合成相关方法
    onSpeechSynthesisComplete(result) {
      console.log('语音合成完成:', result);
      this.$emit('speech-synthesis-complete', result);
    },
    
    onSpeechSynthesisError(error) {
      console.error('语音合成失败:', error);
      this.$emit('speech-synthesis-error', error);
    },
    
    // PDF导出方法
    async exportToPdf() {
      if (this.isExporting) {
        return;
      }
      
      try {
        this.isExporting = true;
        
        // 生成PDF标题
        const title = this.generatePdfTitle();
        
        // 调用PDF导出API
        await exportAndDownloadPdf(this.message, title);
        
        // 发出成功事件
        this.$emit('pdf-export-success', {
          title: title,
          timestamp: new Date()
        });
        
        console.log('PDF导出成功');
        
      } catch (error) {
        console.error('PDF导出失败:', error);
        
        // 发出失败事件
        this.$emit('pdf-export-error', {
          error: error.message || 'PDF导出失败',
          timestamp: new Date()
        });
        
        // 可以在这里添加用户友好的错误提示
        alert('PDF导出失败，请稍后重试');
        
      } finally {
        this.isExporting = false;
      }
    },
    
    // 生成PDF标题
    generatePdfTitle() {
      // 从消息内容中提取前20个字符作为标题
      let title = this.message.replace(/[#*`\n\r]/g, '').trim();
      
      if (title.length > 20) {
        title = title.substring(0, 20) + '...';
      }
      
      if (!title) {
        title = 'AI回复内容';
      }
      
      return title;
    }
  }
}
</script>

<style scoped>
.chat-message {
  display: flex;
  margin-bottom: 20px;
  padding: 0 20px;
}

.user-message {
  justify-content: flex-end;
  flex-direction: row;
}

.user-message .message-avatar {
  order: 2;
}

.user-message .message-content {
  order: 1;
}

.ai-message {
  justify-content: flex-start;
  flex-direction: row;
}

.ai-message .message-avatar {
  order: 1;
}

.ai-message .message-content {
  order: 2;
}

.message-avatar {
  display: flex;
  align-items: flex-start;
  margin: 0 10px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.user-avatar {
  background-color: #007bff;
}

.ai-avatar {
  background-color: #6c757d;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
}

.user-message .message-bubble {
  background-color: #007bff;
  color: white;
  border-bottom-right-radius: 4px;
}

.ai-message .message-bubble {
  background-color: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 4px;
}

.message-text {
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-wrap;
  margin: 0;
}

/* Markdown样式 */
.message-markdown {
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

.message-markdown h1,
.message-markdown h2,
.message-markdown h3,
.message-markdown h4,
.message-markdown h5,
.message-markdown h6 {
  margin: 0.5em 0;
  font-weight: bold;
}

.message-markdown h1 { font-size: 1.5em; }
.message-markdown h2 { font-size: 1.3em; }
.message-markdown h3 { font-size: 1.2em; }
.message-markdown h4 { font-size: 1.1em; }
.message-markdown h5 { font-size: 1em; }
.message-markdown h6 { font-size: 0.9em; }

.message-markdown p {
  margin: 0.5em 0;
}

.message-markdown ul,
.message-markdown ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.message-markdown li {
  margin: 0.2em 0;
}

.message-markdown code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.user-message .message-markdown code {
  background-color: rgba(255, 255, 255, 0.2);
}

.message-markdown pre {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.user-message .message-markdown pre {
  background-color: rgba(255, 255, 255, 0.2);
}

.message-markdown pre code {
  background-color: transparent;
  padding: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.message-markdown blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  margin: 0.5em 0;
  font-style: italic;
  color: #666;
}

.user-message .message-markdown blockquote {
  border-left-color: rgba(255, 255, 255, 0.5);
  color: rgba(255, 255, 255, 0.8);
}

.message-markdown a {
  color: #007bff;
  text-decoration: underline;
}

.user-message .message-markdown a {
  color: #b3d9ff;
}

.message-markdown table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.message-markdown th,
.message-markdown td {
  border: 1px solid #ddd;
  padding: 0.5em;
  text-align: left;
}

.message-markdown th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.user-message .message-markdown th {
  background-color: rgba(255, 255, 255, 0.2);
}

.message-markdown hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1em 0;
}

.user-message .message-markdown hr {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.message-time {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  padding: 0 4px;
}

.user-message .message-time {
  text-align: right;
}

.ai-message .message-time {
  text-align: left;
}

/* 消息操作样式 */
.message-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* PDF导出按钮样式 */
.pdf-export-btn {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pdf-export-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.pdf-export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.pdf-export-btn .loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .message-content {
    max-width: 85%;
  }
  
  .chat-message {
    padding: 0 10px;
  }
}
</style> 