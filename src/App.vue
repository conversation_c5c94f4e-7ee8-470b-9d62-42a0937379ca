<template>
  <div class="app">
    <!-- 主布局容器 -->
    <div class="main-layout">
      <!-- 侧栏 -->
      <Sidebar 
        @topic-select="handleTopicSelect"
        @clear-chat="clearCurrentChat"
        @export-chat="exportCurrentChat"
        @sidebar-toggle="handleSidebarToggle"
      />
      
      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 紧凑的头部 -->
        <div class="compact-header">
          <div class="header-left">
            <h1 class="app-title">AI 英语学习助手</h1>
            <ChatModeToggle 
              v-model="currentMode"
              @mode-change="handleModeChange"
              @topic-select="handleTopicSelect"
              :compact="true"
            />
          </div>
          <div class="header-actions">
            <button 
              @click="showSpeechSettings = true" 
              class="settings-btn"
              title="语音设置"
            >
              🎤
            </button>
          </div>
        </div>

    <!-- 聊天区域 -->
    <div class="chat-container">
      <!-- 消息列表 -->
      <div class="messages-container" ref="messagesContainer">
        <div v-if="currentMessages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <div class="welcome-icon">{{ currentMode === 'rag' ? '🧠' : '🤖' }}</div>
            <h2>
              {{ currentMode === 'rag' ? '英语学习助手' : 'AI 英语导师' }}
            </h2>
            <p>{{ currentMode === 'rag' ? '基于专业英语知识库为您提供学习指导' : '我可以帮助您提升英语听说读写各项技能' }}</p>
            
            <div v-if="currentMode === 'rag'" class="rag-features">
              <h3>🎯 英语学习领域</h3>
              <ul>
                <li>📚 英语语法详解</li>
                <li>💬 口语表达练习</li>
                <li>📖 阅读理解技巧</li>
                <li>✍️ 写作能力提升</li>
                <li>🎧 听力训练方法</li>
                <li>📝 词汇量扩展</li>
              </ul>
            </div>
            
            <div v-else class="normal-features">
              <h3>💬 我可以帮助您</h3>
              <ul>
                <li>英语语法问题解答</li>
                <li>单词发音和用法指导</li>
                <li>英语对话练习</li>
                <li>英语学习方法建议</li>
                <li>英语考试备考指导</li>
              </ul>
            </div>
            
            <p class="welcome-tip">{{ getWelcomeTip() }}</p>
          </div>
        </div>

        <!-- 历史消息 -->
        <template v-if="currentMode === 'normal'">
          <ChatMessage
            v-for="message in currentMessages"
            :key="message.id"
            :message="message.content"
            :is-user="message.isUser"
            :timestamp="message.timestamp"
          />
        </template>

        <template v-else>
          <RagMessage
            v-for="message in currentMessages"
            :key="message.id"
            :message="message.content"
            :is-user="message.isUser"
            :timestamp="message.timestamp"
            :sources="message.sources"
          />
        </template>

        <!-- AI 正在回复的消息 -->
        <div v-if="isAiTyping" class="chat-message ai-message">
          <div class="message-avatar">
            <div class="avatar" :class="currentMode === 'rag' ? 'rag-avatar' : 'ai-avatar'">
              {{ currentMode === 'rag' ? '🧠' : 'AI' }}
            </div>
          </div>
          <div class="message-content">
            <div class="message-bubble" :class="{ 'rag-bubble': currentMode === 'rag' }">
              <div class="ai-typing-content">
                <div class="ai-response-text message-markdown" v-html="currentAiResponseRendered"></div>
                <LoadingDots v-if="isStreaming" />
              </div>
            </div>
          </div>
        </div>
      </div>

                <!-- 输入框 -->
          <ChatInput
            :disabled="isAiTyping"
            @send-message="sendMessage"
            :placeholder="getInputPlaceholder()"
          />
        </div>
      </div>
    </div>

    <!-- 连接状态提示 -->
    <div v-if="connectionError" class="connection-error">
      <div class="error-content">
        <span class="error-icon">⚠️</span>
        <span>{{ errorMessage }}</span>
      </div>
    </div>

    <!-- 语音设置组件 -->
    <SpeechSettings 
      :show="showSpeechSettings"
      @close="showSpeechSettings = false"
      @settings-changed="handleSpeechSettingsChanged"
    />
  </div>
</template>

<script>
import ChatMessage from './components/ChatMessage.vue'
import RagMessage from './components/RagMessage.vue'
import ChatInput from './components/ChatInput.vue'
import LoadingDots from './components/LoadingDots.vue'
import ChatModeToggle from './components/ChatModeToggle.vue'
import SpeechSettings from './components/SpeechSettings.vue'
import Sidebar from './components/Sidebar.vue'
import { chatWithSSE } from './api/chatApi.js'
import { ragChatStream, ragChat } from './api/ragApi.js'
import { generateMemoryId } from './utils/index.js'
import { marked } from 'marked'

export default {
  name: 'App',
  components: {
    ChatMessage,
    RagMessage,
    ChatInput,
    LoadingDots,
    ChatModeToggle,
    SpeechSettings,
    Sidebar
  },
  data() {
    return {
      currentMode: 'normal', // 'normal' 或 'rag'
      normalMessages: [],
      ragMessages: [],
      memoryId: null,
      ragMemoryId: null,
      isAiTyping: false,
      isStreaming: false,
      currentAiResponse: '',
      currentEventSource: null,
      connectionError: false,
      errorMessage: '',
      showSearchSuggestions: false,
      searchSuggestions: [
        '如何快速提高英语口语水平？',
        '英语语法基础知识有哪些？',
        '怎样有效背英语单词？',
        '英语听力训练的方法',
        '如何准备英语考试？',
        '商务英语沟通技巧'
      ],
      showSpeechSettings: false,
      speechSettings: {
        selectedVoice: '',
        autoPlayEnabled: false,
        voiceQuality: 'standard',
        speechRate: 1.0
      }
    }
  },
  computed: {
    currentMessages() {
      return this.currentMode === 'normal' ? this.normalMessages : this.ragMessages
    },
    
    currentAiResponseRendered() {
      if (!this.currentAiResponse) return ''
      marked.setOptions({
        breaks: true,
        gfm: true,
        sanitize: false,
        highlight: function(code, lang) {
          return code
        }
      })
      return marked(this.currentAiResponse)
    }
  },
  methods: {
    handleModeChange(mode) {
      console.log('切换到模式:', mode)
      
      // 停止当前的AI回复
      if (this.isAiTyping && this.currentEventSource) {
        this.currentEventSource.close()
        this.finishAiResponse()
      }
      
      // 清除错误状态
      this.connectionError = false
      
      // 根据模式显示搜索建议
      if (mode === 'rag' && this.ragMessages.length === 0) {
        this.showSearchSuggestions = true
      }
    },
    
    handleTopicSelect(topic) {
      // 自动填入主题相关的问题
      const topicQuestions = {
        '语法学习': '英语语法的基础知识有哪些？如何系统地学习英语语法？',
        '口语练习': '如何有效提高英语口语水平？有哪些实用的练习方法？',
        '听力训练': '英语听力训练的有效方法有哪些？如何提高听力理解能力？',
        '阅读理解': '如何提高英语阅读理解能力？有哪些阅读技巧和方法？',
        '写作技巧': '英语写作的基本技巧有哪些？如何写出优秀的英语文章？',
        '词汇扩展': '如何高效背诵和记忆英语单词？词汇量提升的方法有哪些？',
        '考试备考': '如何准备英语考试？托福、雅思、四六级的备考策略是什么？',
        '商务英语': '商务英语的核心内容有哪些？职场英语沟通技巧是什么？'
      }
      
      const question = topicQuestions[topic] || `请介绍一下${topic}相关的知识`
      this.sendMessage(question)
    },
    
    sendMessage(message) {
      // 添加用户消息
      this.addMessage(message, true)
      
      // 隐藏搜索建议
      this.showSearchSuggestions = false
      
      // 开始AI回复
      this.startAiResponse(message)
    },
    
    addMessage(content, isUser = false, sources = null) {
      const message = {
        id: Date.now() + Math.random(),
        content,
        isUser,
        timestamp: new Date(),
        sources: sources || []
      }
      
      if (this.currentMode === 'normal') {
        this.normalMessages.push(message)
      } else {
        this.ragMessages.push(message)
      }
      
      this.scrollToBottom()
    },
    
    startAiResponse(userMessage) {
      this.isAiTyping = true
      this.isStreaming = true
      this.currentAiResponse = ''
      this.connectionError = false
      
      // 关闭之前的连接
      if (this.currentEventSource) {
        this.currentEventSource.close()
      }
      
      // 根据模式选择不同的API
      if (this.currentMode === 'normal') {
        this.startNormalChat(userMessage)
      } else {
        this.startRagChat(userMessage)
      }
    },
    
    startNormalChat(userMessage) {
      this.currentEventSource = chatWithSSE(
        this.memoryId,
        userMessage,
        this.handleAiMessage,
        this.handleAiError,
        this.handleAiClose
      )
    },
    
    startRagChat(userMessage) {
      // 使用RAG流式聊天
      this.currentEventSource = ragChatStream(
        this.ragMemoryId,
        userMessage,
        this.handleAiMessage,
        this.handleRagError,
        () => this.handleAiClose('rag')
      )
    },
    
    handleAiMessage(data) {
      this.currentAiResponse += data
      this.scrollToBottom()
    },
    
    handleAiError(error) {
      console.error('普通聊天出错:', error)
      this.errorMessage = '英语学习服务连接失败，请检查后端服务'
      this.connectionError = true
      this.finishAiResponse()
      this.autoHideError()
    },
    
    handleRagError(error) {
      console.error('RAG聊天出错:', error)
      this.errorMessage = '英语学习知识库服务连接失败，请检查后端服务'
      this.connectionError = true
      this.finishAiResponse()
      this.autoHideError()
    },
    
    handleAiClose(mode = 'normal') {
      if (mode === 'rag') {
        this.finishRagResponse()
      } else {
        this.finishAiResponse()
      }
    },
    
    finishAiResponse() {
      this.isStreaming = false
      
      if (this.currentAiResponse.trim()) {
        this.addMessage(this.currentAiResponse.trim(), false)
      }
      
      this.resetAiState()
    },
    
    finishRagResponse() {
      this.isStreaming = false
      
      if (this.currentAiResponse.trim()) {
        // RAG响应可能包含来源信息，这里简化处理
        // 实际应用中应该从API响应中获取sources
        this.addMessage(this.currentAiResponse.trim(), false, [])
      }
      
      this.resetAiState()
    },
    
    resetAiState() {
      this.isAiTyping = false
      this.currentAiResponse = ''
      this.connectionError = false
      
      if (this.currentEventSource) {
        this.currentEventSource.close()
        this.currentEventSource = null
      }
    },
    
    autoHideError() {
      setTimeout(() => {
        this.connectionError = false
      }, 5000)
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },
    
    initializeChat() {
      this.memoryId = generateMemoryId()
      this.ragMemoryId = generateMemoryId()
      console.log('普通聊天室ID:', this.memoryId)
      console.log('RAG聊天室ID:', this.ragMemoryId)
    },
    
    getWelcomeTip() {
      if (this.currentMode === 'rag') {
        return '💡 提示: 点击上方的主题标签快速开始，或直接输入您的英语学习问题'
      } else {
        return '💡 提示: 您可以询问任何英语学习相关的问题，我会为您提供专业指导'
      }
    },
    
    getInputPlaceholder() {
      if (this.currentMode === 'rag') {
        return '请输入您的英语学习问题（如：语法、口语、听力等）...'
      } else {
        return '请输入您的英语学习问题...'
      }
    },
    
    handleSuggestionClick(suggestion) {
      this.sendMessage(suggestion)
    },
    
    // 语音设置相关方法
    handleSpeechSettingsChanged(settings) {
      this.speechSettings = { ...settings };
      console.log('语音设置已更新:', settings);
    },
    
    // 侧栏相关方法
    handleSidebarToggle(isCollapsed) {
      console.log('侧栏状态切换:', isCollapsed ? '收起' : '展开');
    },
    
    clearCurrentChat() {
      if (this.currentMode === 'normal') {
        this.normalMessages = [];
      } else {
        this.ragMessages = [];
      }
      this.resetAiState();
      console.log('已清空当前对话');
    },
    
    exportCurrentChat() {
      const messages = this.currentMessages;
      if (messages.length === 0) {
        alert('当前没有对话内容可导出');
        return;
      }
      
      const content = messages.map(msg => {
        const time = new Date(msg.timestamp).toLocaleString();
        const sender = msg.isUser ? '用户' : 'AI助手';
        return `[${time}] ${sender}: ${msg.content}`;
      }).join('\n\n');
      
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `英语学习对话_${new Date().toISOString().slice(0, 10)}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      console.log('对话已导出');
    }
  },
  
  mounted() {
    this.initializeChat()
  },
  
  beforeUnmount() {
    if (this.currentEventSource) {
      this.currentEventSource.close()
    }
  }
}
</script>

<style scoped>
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0f0f0;
}

.main-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #fff;
}

.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e1e5e9;
  background-color: #fff;
  min-height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.settings-btn {
  padding: 8px 12px;
  background: #4CAF50;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 36px;
}

.settings-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.app-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f8f9fa;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 20px;
}

.welcome-content {
  text-align: center;
  max-width: 500px;
  color: #666;
}

.welcome-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.welcome-content h2 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
}

.welcome-content h3 {
  font-size: 16px;
  margin: 20px 0 10px 0;
  color: #495057;
}

.welcome-content p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.rag-features, .normal-features {
  text-align: left;
  margin: 20px 0;
}

.rag-features ul, .normal-features ul {
  margin: 10px 0;
  padding-left: 0;
  list-style: none;
}

.rag-features li, .normal-features li {
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #007bff;
}

.welcome-tip {
  margin-top: 20px;
  padding: 12px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 8px;
  color: #1976d2;
  font-size: 13px;
}

/* AI 正在回复时的消息样式 */
.chat-message {
  display: flex;
  margin-bottom: 20px;
  padding: 0 20px;
}

.ai-message {
  justify-content: flex-start;
  flex-direction: row;
}

.message-avatar {
  display: flex;
  align-items: flex-start;
  margin: 0 10px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
}

.ai-avatar {
  background-color: #6c757d;
}

.rag-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-size: 16px;
}

.message-content {
  max-width: 70%;
  min-width: 100px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
  word-break: break-word;
  background-color: #f1f3f4;
  color: #333;
  border-bottom-left-radius: 4px;
}

.rag-bubble {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e9ecef;
}

.ai-typing-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ai-response-text {
  font-size: 14px;
  line-height: 1.5;
}

/* AI实时回复的Markdown样式 */
.ai-response-text.message-markdown h1,
.ai-response-text.message-markdown h2,
.ai-response-text.message-markdown h3,
.ai-response-text.message-markdown h4,
.ai-response-text.message-markdown h5,
.ai-response-text.message-markdown h6 {
  margin: 0.5em 0;
  font-weight: bold;
}

.ai-response-text.message-markdown h1 { font-size: 1.5em; }
.ai-response-text.message-markdown h2 { font-size: 1.3em; }
.ai-response-text.message-markdown h3 { font-size: 1.2em; }
.ai-response-text.message-markdown h4 { font-size: 1.1em; }
.ai-response-text.message-markdown h5 { font-size: 1em; }
.ai-response-text.message-markdown h6 { font-size: 0.9em; }

.ai-response-text.message-markdown p {
  margin: 0.5em 0;
}

.ai-response-text.message-markdown ul,
.ai-response-text.message-markdown ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.ai-response-text.message-markdown li {
  margin: 0.2em 0;
}

.ai-response-text.message-markdown code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.ai-response-text.message-markdown pre {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1em;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0.5em 0;
}

.ai-response-text.message-markdown pre code {
  background-color: transparent;
  padding: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.ai-response-text.message-markdown blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1em;
  margin: 0.5em 0;
  font-style: italic;
  color: #666;
}

.ai-response-text.message-markdown a {
  color: #007bff;
  text-decoration: underline;
}

.ai-response-text.message-markdown table {
  border-collapse: collapse;
  width: 100%;
  margin: 0.5em 0;
}

.ai-response-text.message-markdown th,
.ai-response-text.message-markdown td {
  border: 1px solid #ddd;
  padding: 0.5em;
  text-align: left;
}

.ai-response-text.message-markdown th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.ai-response-text.message-markdown hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 1em 0;
}

.connection-error {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff4444;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  z-index: 1000;
  animation: slideDown 0.3s ease-out;
  max-width: 80%;
  text-align: center;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 16px;
}



@keyframes slideDown {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }
  
  .compact-header {
    padding: 10px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    min-height: auto;
  }
  
  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    width: 100%;
  }
  
  .app-title {
    font-size: 18px;
  }
  
  .messages-container {
    padding: 15px 0;
  }
  
  .welcome-content {
    padding: 0 10px;
    max-width: 90%;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .chat-message {
    padding: 0 10px;
  }
}
</style> 