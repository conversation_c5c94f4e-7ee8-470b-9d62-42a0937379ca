# AI 英语学习助手 - 前端项目

这是一个基于 Vue3 开发的 AI 英语学习助手前端应用，旨在帮助用户提升英语听说读写各项技能，提供个性化的英语学习指导。

## 功能特点

- 🤖 **智能对话**：基于 SSE (Server-Sent Events) 的实时聊天体验
- 🧠 **RAG 增强问答**：基于英语学习知识库的专业问答，显示学习资料来源和相似度（NEW！）
- 💬 **双模式聊天**：支持普通英语对话和知识问答两种模式切换（NEW！）
- 📱 **响应式设计**：适配桌面和移动设备
- ⚡ **实时流式响应**：AI 回复实时显示，提供流畅的用户体验
- 🔄 **自动滚动**：新消息自动滚动到底部
- 🎨 **美观界面**：现代化 UI 设计，良好的用户体验
- 📝 **Markdown 支持**：AI 回复支持完整的 Markdown 格式，包括代码高亮、表格、列表等
- 📚 **学习资料展示**：RAG模式下可查看回复的学习资料来源和相关文档（NEW！）
- 🎯 **主题快选**：一键选择英语学习主题快速开始对话（NEW！）

## 技术栈

- **Vue 3** - 前端框架
- **Vite** - 构建工具
- **Axios** - HTTP 请求库
- **SSE (Server-Sent Events)** - 实时通信
- **Marked** - Markdown 解析和渲染
- **CSS3** - 样式和动画

## 项目结构

```
ai-english-learning-frontend/
├── public/                  # 静态资源
├── src/
│   ├── api/                # API 接口
│   │   ├── chatApi.js      # 普通聊天接口
│   │   └── ragApi.js       # RAG 问答接口 (NEW!)
│   ├── components/         # Vue 组件
│   │   ├── ChatMessage.vue # 普通聊天消息组件
│   │   ├── RagMessage.vue  # RAG 消息组件 (NEW!)
│   │   ├── ChatInput.vue   # 输入框组件
│   │   ├── ChatModeToggle.vue # 模式切换组件 (NEW!)
│   │   └── LoadingDots.vue # 加载动画组件
│   ├── utils/              # 工具函数
│   │   └── index.js        # 通用工具
│   ├── App.vue             # 主应用组件
│   └── main.js             # 应用入口
├── index.html              # HTML 模板
├── vite.config.js          # Vite 配置
├── package.json            # 依赖管理
└── README.md               # 项目说明
```

## 开始使用

### 前置要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 运行。

### 构建生产版本

```bash
npm run build
```

构建文件将输出到 `dist` 目录。

### 预览生产版本

```bash
npm run preview
```

## 后端接口

应用需要连接到后端服务器获取 AI 回复。后端接口信息：

- **基础 URL**: `http://localhost:8081/api`

### 普通聊天接口
- **聊天接口**: `GET /ai/chat`
  - 参数：
    - `memoryId`: 聊天室 ID（数字）
    - `message`: 用户消息（字符串）
  - 返回：SSE 流式响应

### RAG 问答接口 (NEW!)
- **RAG 聊天**: `POST /ai/rag/chat`
  - 请求体：`{"message": "用户问题", "context": "可选上下文"}`
  - 返回：包含答案和知识源的 JSON 响应

- **RAG 流式聊天**: `GET /ai/rag/chat-stream`
  - 参数：`memoryId`, `message`
  - 返回：SSE 流式响应

- **知识库搜索**: `POST /ai/rag/search`
  - 请求体：`{"query": "搜索词", "maxResults": 5, "minScore": 0.7}`
  - 返回：匹配的知识片段

- **知识库统计**: `GET /ai/rag/stats`
  - 返回：知识库统计信息

### 后端服务器

确保后端 SpringBoot 服务正在运行在 `http://localhost:8081`。

## 主要功能

### 1. 双模式聊天 (NEW!)

- **普通聊天模式**：与AI英语导师进行日常英语对话，适合口语练习
- **知识问答模式**：基于英语学习知识库的问答，显示学习资料来源
- 模式切换后保持各自的聊天历史
- 独立的会话管理和状态维护

### 2. RAG 增强功能 (NEW!)

- 基于英语学习知识库的专业问答
- 显示回复的学习资料来源和相似度分数
- 支持展开/收起查看源文档详情
- 英语学习主题标签快速开始功能
- 实时英语学习资源统计信息显示

### 3. 界面特性

- 用户消息显示在右侧（蓝色气泡）
- AI 回复显示在左侧（普通模式：灰色，RAG模式：渐变紫色）
- 实时显示 AI 正在输入的状态
- 自动滚动到最新消息
- 响应式设计，适配移动端

### 4. 错误处理

- 连接失败时显示错误提示
- 区分英语学习服务和RAG服务的错误
- 自动重连机制
- 优雅的错误恢复

### 4. Markdown 支持

- **完整的 Markdown 语法**：支持标题、列表、代码块、表格等
- **代码高亮**：编程代码自动应用语法高亮
- **实时渲染**：AI 回复时 Markdown 内容实时渲染
- **响应式设计**：Markdown 内容在各种设备上完美显示
- **主题适配**：在不同背景色下都有良好的视觉效果

支持的 Markdown 功能包括：
- 标题 (H1-H6)
- 粗体、斜体、删除线
- 行内代码和代码块
- 无序和有序列表
- 链接和引用
- 表格和分割线

## 开发指南

### 添加新功能

1. 在 `src/components/` 目录下创建新组件
2. 在 `src/utils/` 目录下添加工具函数
3. 在 `src/api/` 目录下添加 API 接口

### 样式自定义

项目使用 CSS3 和 Flexbox 布局，样式文件分布在各个组件中。主要的样式变量：

- 主色调：`#007bff`
- 背景色：`#f0f0f0`
- RAG模式渐变：`linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- 知识源背景：`linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)`

## RAG 功能使用指南

详细的RAG功能使用方法和界面说明，请参考：[RAG前端功能指南](./FRONTEND_RAG_GUIDE.md)
- 消息气泡：`#f1f3f4`（AI）、`#007bff`（用户）

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 贡献指南

1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。

## 英语学习功能说明

详细的英语学习功能和使用指南，请参考：[英语学习功能指南](./ENGLISH_LEARNING_GUIDE.md) 